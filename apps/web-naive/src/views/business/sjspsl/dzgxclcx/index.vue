<script lang="ts" setup name="user-index">
import type { BaseFormComponentType, VbenFormSchema } from '@vben/common-ui';

import type { ViewInvokeParam } from './data';

import { nextTick, onMounted, ref, watch } from 'vue';

import { Page, useVbenForm } from '@vben/common-ui';

import { defaultFormConfig } from '#/adapter/form';
import { apiGaggsjGadzgxcl, apiGaggsjViewInvokeParam } from '#/api';

// 定义接口返回数据类型
import { BaseQueryFormSchema, generateFormSchemaFromParams } from './data';

interface StructDataToImage {
  index: number;
  logWjbh: string;
  base64wj: string;
  orgiUrl: string;
  orgiDownloadWjbh: string;
  structList: any[];
}

interface ApiResponse {
  structDataToImageList: StructDataToImage[];
}

// 响应式数据
const loading = ref(false);
const resultData = ref<StructDataToImage[]>([]);
const activeTab = ref('1');

// 动态表单 schema
const dynamicFormSchema = ref([...BaseQueryFormSchema]) as Ref<
  VbenFormSchema<BaseFormComponentType>[]
>;

// 监听 exchangeServiceId 变化，动态加载查询条件
const loadDynamicFields = async (exchangeServiceId: string) => {
  if (!exchangeServiceId) {
    dynamicFormSchema.value = [...BaseQueryFormSchema];
    return;
  }

  try {
    const params: ViewInvokeParam[] =
      await apiGaggsjViewInvokeParam(exchangeServiceId);
    const dynamicFields = generateFormSchemaFromParams(params);
    dynamicFormSchema.value = [...BaseQueryFormSchema, ...dynamicFields];
    // 重新初始化表单，只保留 exchangeServiceId
    const currentValues = await formApi.getValues();
    const currentExchangeServiceId = currentValues.exchangeServiceId;

    // 完全重置表单
    await formApi.resetForm();

    // 重新设置 exchangeServiceId 和新字段的默认值
    const newValues: Record<string, any> = {
      exchangeServiceId: currentExchangeServiceId,
    };

    // 添加动态字段的默认值
    params.forEach((param) => {
      if (param.defaultValue) {
        newValues[param.field] = param.defaultValue;
      }
    });

    // 设置新的表单值
    await formApi.setValues(newValues);
  } catch (error) {
    console.error('加载动态字段失败:', error);
    dynamicFormSchema.value = [...BaseQueryFormSchema];
  }
};

// 表单配置
const [Form, formApi] = useVbenForm({
  schema: dynamicFormSchema.value,
  ...defaultFormConfig,
  handleSubmit: async (values) => {
    await handleQuery(values);
  },
  handleValuesChange: async (values, changedFields) => {
    // 监听 exchangeServiceId 字段变化
    if (changedFields.includes('exchangeServiceId')) {
      await loadDynamicFields(values.exchangeServiceId);
    }
  },
});

// 监听 dynamicFormSchema 变化，动态更新表单 schema
watch(
  dynamicFormSchema,
  (newSchema) => {
    formApi.setState({ schema: newSchema });
  },
  { deep: true },
);

// 初始化表单默认值
const initFormDefaults = async () => {
  // 等待表单挂载完成
  await nextTick();

  // 设置基础表单的默认值
  const defaultValues: Record<string, any> = {};

  // 遍历基础 schema 设置默认值
  BaseQueryFormSchema.forEach((schema) => {
    if (schema.defaultValue !== undefined) {
      defaultValues[schema.fieldName] = schema.defaultValue;
    }
  });

  if (Object.keys(defaultValues).length > 0) {
    await formApi.setValues(defaultValues);
  }
};

// 组件挂载后初始化默认值
onMounted(() => {
  initFormDefaults();
});

// 查询处理函数
const handleQuery = async (formValues: any) => {
  try {
    loading.value = true;
    const { exchangeServiceId, ...requestMap } = formValues;
    const result: ApiResponse = await apiGaggsjGadzgxcl({
      exchangeServiceId,
      requestMap,
    });

    resultData.value = result.structDataToImageList || [];
    // 设置默认选中第一个tab
    if (resultData.value.length > 0) {
      activeTab.value = '1';
    }
  } catch (error) {
    console.error('查询失败:', error);
    resultData.value = [];
  } finally {
    loading.value = false;
  }
};
</script>
<template>
  <Page>
    <!-- 查询表单 -->
    <div class="bg-card border-border mb-4 rounded-lg border p-4 shadow-sm">
      <Form />
    </div>

    <!-- 结果展示区域 -->
    <div
      v-if="resultData.length > 0"
      class="bg-card border-border flex h-full rounded-lg border shadow-sm"
    >
      <!-- 左侧Tab导航 -->
      <div class="border-border w-48 border-r">
        <a-tabs
          v-model:active-key="activeTab"
          tab-position="left"
          class="h-full"
        >
          <a-tab-pane
            v-for="(_item, index) in resultData"
            :key="String(index + 1)"
            :tab="String(index + 1)"
          />
        </a-tabs>
      </div>

      <!-- 右侧内容区域 -->
      <div class="flex-1 p-6">
        <div v-for="(item, index) in resultData" :key="index">
          <div v-if="activeTab === String(index + 1)">
            <div v-if="item.base64wj" class="text-center">
              <img
                :src="`data:image/jpeg;base64,${item.base64wj}`"
                :alt="`图片 ${index + 1}`"
                class="border-border h-auto max-w-full rounded-lg border shadow-sm"
                style="max-height: 80vh"
              />
            </div>
            <div v-else class="text-muted-foreground py-8 text-center">
              暂无图片数据
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div
      v-if="loading"
      class="bg-card border-border flex-center rounded-lg border py-12 shadow-sm"
    >
      <a-spin size="large" />
    </div>
  </Page>
</template>
<style lang="scss" scoped></style>
