import type { BaseFormComponentType, VbenFormSchema } from '@vben/common-ui';

import { apiFwtDmGgListByDmlx } from '#/api';

// 基础表单 Schema（只包含业务及材料类型选择）
export const BaseQueryFormSchema: VbenFormSchema<BaseFormComponentType>[] = [
  {
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择',
      options: [],
      api: apiFwtDmGgListByDmlx,
      params: {
        dmlx: 'dzgxcllx',
      },
      afterFetch: (options: any[]) => {
        if (!options) {
          return [];
        }
        return options.map((item: any) => ({
          text: item.dmmc,
          value: item.kzbzb,
        }));
      },
    },
    fieldName: 'exchangeServiceId',
    label: '业务材料类型',
    rules: 'required',
  },
];

// ViewInvokeParam 接口类型定义
export interface ViewInvokeParam {
  field: string;
  chn: string;
  component: string;
  dictType?: string;
  dictionary?: Record<string, string>;
  required: boolean;
  defaultValue?: string;
}

// 根据 ViewInvokeParam 生成表单字段的函数
export function generateFormSchemaFromParams(
  params: ViewInvokeParam[],
): VbenFormSchema<BaseFormComponentType>[] {
  return params.map((param) => {
    const baseSchema: VbenFormSchema<BaseFormComponentType> = {
      fieldName: param.field,
      label: param.chn,
      rules: param.required ? 'required' : undefined,
      ...(param.defaultValue && { defaultValue: param.defaultValue }),
    };

    // 根据类型生成不同的组件
    if (param.component === 'Select' && param.dictionary) {
      return {
        ...baseSchema,
        component: 'Select',
        componentProps: {
          placeholder: `请选择${param.chn}`,
          options: Object.entries(param.dictionary).map(([key, value]) => ({
            label: value,
            value: key,
          })),
        },
      };
    } else if (param.component === 'ApiSelect' && param.dictType) {
      return {
        ...baseSchema,
        component: 'ApiSelect',
        componentProps: {
          placeholder: `请选择${param.chn}`,
          params: {
            type: param.dictType,
          },
        },
      };
    } else {
      // 默认使用 Input 组件
      return {
        ...baseSchema,
        component: 'Input',
        componentProps: {
          placeholder: `请输入${param.chn}`,
        },
      };
    }
  });
}
