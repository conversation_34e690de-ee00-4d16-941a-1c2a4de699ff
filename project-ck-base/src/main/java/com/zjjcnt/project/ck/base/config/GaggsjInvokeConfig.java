package com.zjjcnt.project.ck.base.config;

import com.zjjcnt.project.ck.base.constant.GaggsjConstants;
import com.zjjcnt.project.ck.base.wscall.gaggsj.config.GaggsjConfigParameter;
import com.zjjcnt.project.ck.base.wscall.gaggsj.config.GaggsjConfigParameterImpl;
import com.zjjcnt.project.ck.base.wscall.gaggsj.config.MapSendDataBuilderImpl;
import com.zjjcnt.project.ck.base.wscall.gaggsj.config.ViewInvokeParam;
import com.zjjcnt.project.ck.base.wscall.gaggsj.convertor.GaggsjPlatformDataLicenceConvertor;
import com.zjjcnt.project.ck.base.wscall.gaggsj.convertor.StructTranslater;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公安公共数据配置
 *
 * <AUTHOR>
 * @date 2025-07-14 16:19:00
 */
@Configuration
public class GaggsjInvokeConfig {

    private final String serviceName = "常口系统";
    private final String appUrl = "http://41.188.23.151:8080/zdpyc_server/queryInfo/queryInfo.do";
    private final String appId = "751624B7F20C9A0AE0533C03760AFEA7";

    @Bean(name = "gaggsjConfigParameterWGX33-00001887")
    public GaggsjConfigParameter gaggsjConfigParameterWGX3300001881() {
        GaggsjConfigParameterImpl gaggsjConfigParameter = getCommonGaggsjConfigParameterImpl("WGX33-00001887");
        gaggsjConfigParameter.setExchangeServiceName("省卫生计生委出生医学证明");

        List<ViewInvokeParam> viewInvokeParamList = new ArrayList<>();
        viewInvokeParamList.add(new ViewInvokeParam("csbh", "出生编号"));
        viewInvokeParamList.add(new ViewInvokeParam("fsfz", "父身份证"));
        viewInvokeParamList.add(new ViewInvokeParam("msfz", "母身份证"));
        gaggsjConfigParameter.setViewInvokeParamList(viewInvokeParamList);
        gaggsjConfigParameter.setSendDataBuilder(new MapSendDataBuilderImpl());
        GaggsjPlatformDataLicenceConvertor dataLicenceConvertor = new GaggsjPlatformDataLicenceConvertor();

        dataLicenceConvertor.setUrlPath("ELC_LICENCE_FILE@URL");
        dataLicenceConvertor.setAutoPdfUpload(true);
        dataLicenceConvertor.setStructPath("ELC_LICENCE_STRUCT@DATA");

        dataLicenceConvertor.setPdf2JpgWidth(0);
        dataLicenceConvertor.setPdf2JpgScale(1);
        dataLicenceConvertor.setPdf2JpgQuality(0.9f);

        gaggsjConfigParameter.setDataConvertor(dataLicenceConvertor);

        return gaggsjConfigParameter;
    }


    @Bean(name = "gaggsjConfigParameterWGX33-00000236")
    public GaggsjConfigParameter gaggsjConfigParameterWGX3300000236() {
        GaggsjConfigParameterImpl gaggsjConfigParameter = getCommonGaggsjConfigParameterImpl("WGX33-00000236");
        gaggsjConfigParameter.setExchangeServiceName("婚姻登记信息");

        List<ViewInvokeParam> viewInvokeParamList = new ArrayList<>();
        viewInvokeParamList.add(new ViewInvokeParam("cardId", "个人身份证号"));

        ViewInvokeParam viewInvokeParam = new ViewInvokeParam("sex", "性别");
        viewInvokeParam.setComponent(GaggsjConstants.COMPONENT_SELECT);
        Map<String, String> dictionary = new HashMap<>();
        dictionary.put("M", "男");
        dictionary.put("F", "女");
        viewInvokeParam.setDictionary(dictionary);
        viewInvokeParamList.add(viewInvokeParam);

        gaggsjConfigParameter.setViewInvokeParamList(viewInvokeParamList);
        MapSendDataBuilderImpl mapSendDataBuilder = new MapSendDataBuilderImpl();
        mapSendDataBuilder.setRequiredField("cardId,sex");

        gaggsjConfigParameter.setSendDataBuilder(mapSendDataBuilder);
        GaggsjPlatformDataLicenceConvertor dataLicenceConvertor = new GaggsjPlatformDataLicenceConvertor();

        dataLicenceConvertor.setUrlPath("");
        dataLicenceConvertor.setAutoPdfUpload(true);
        dataLicenceConvertor.setStructPath("_SELF");

        dataLicenceConvertor.setPdf2JpgWidth(0);
        dataLicenceConvertor.setPdf2JpgScale(1);
        dataLicenceConvertor.setPdf2JpgQuality(0.9f);

        List<StructTranslater> translaterList = new ArrayList<>();
        translaterList.add(new StructTranslater("id", "编号"));
        translaterList.add(new StructTranslater("", ""));
        translaterList.add(new StructTranslater("businessType", "登记类型"));
        translaterList.add(new StructTranslater("registrationOrg", "登记机关名称"));
        translaterList.add(new StructTranslater("mName", "男方姓名"));
        translaterList.add(new StructTranslater("mCardId", "男方身份证号码"));
        translaterList.add(new StructTranslater("fName", "女方姓名"));
        translaterList.add(new StructTranslater("fCardId", "女方身份证号码"));
        translaterList.add(new StructTranslater("registrationDate", "登记日期"));
        translaterList.add(new StructTranslater("tong_time", "数据归集时间"));

        dataLicenceConvertor.setTranslaterList(translaterList);

        gaggsjConfigParameter.setDataConvertor(dataLicenceConvertor);

        return gaggsjConfigParameter;
    }


    private GaggsjConfigParameterImpl getCommonGaggsjConfigParameterImpl(String configId) {
        GaggsjConfigParameterImpl gaggsjConfigParameter = new GaggsjConfigParameterImpl();
        gaggsjConfigParameter.setConfigId(configId);
        gaggsjConfigParameter.setExchangeServiceId(configId);
        gaggsjConfigParameter.setInvokeServiceName(serviceName);
        gaggsjConfigParameter.setAppUrl(appUrl);
        gaggsjConfigParameter.setAppId(appId);
        gaggsjConfigParameter.setPowerMatters("");
        gaggsjConfigParameter.setSubPowerMatters("");
        return gaggsjConfigParameter;
    }

}