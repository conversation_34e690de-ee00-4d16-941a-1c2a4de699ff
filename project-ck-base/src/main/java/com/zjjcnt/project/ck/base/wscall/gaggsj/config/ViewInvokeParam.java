package com.zjjcnt.project.ck.base.wscall.gaggsj.config;

import com.zjjcnt.project.ck.base.constant.GaggsjConstants;
import lombok.Data;

import java.util.Map;

/**
 *
 * 公共页面调用时的公共页面
 * Created by fudongwz on 2018/11/14.
 */
@Data
public class ViewInvokeParam {

    /**
     * 字段
     */
    private String field;

    /**
     * 字段中文描述
     */
    private String chn;

    /**
     * 前端组件类型
     */
    private String component = GaggsjConstants.COMPONENT_INPUT;

    /**
     * 字典类型
     */
    private String dictType;

    /**
     * 字典值，先根据指定字典值进行翻译，再更具zxfg进行解析转化
     */
    private Map<String, String> dictionary;

    private boolean required = true;

    public ViewInvokeParam() {
    }

    public ViewInvokeParam(String field, String chn) {
        this.field = field;
        this.chn = chn;
    }

}
