# 动态生成查询条件

## 任务描述
根据业务及材料类型字段的选择，查询 `apiGaggsjViewInvokeParam` 接口，获取查询条件，根据 component 类型生成查询组件：
- 如果是 Select，根据 dictionary 生成下拉框，key 是值，value 是文字
- 根据 required 字段校验必填

## 接口信息
- 接口：`apiGaggsjViewInvokeParam`
- 参数：`exchangeServiceId: string`
- 返回：`ViewInvokeParam[]`

## ViewInvokeParam 结构
```typescript
interface ViewInvokeParam {
  field: string;        // 字段名
  chn: string;         // 字段中文描述  
  type: string;        // 组件类型（默认"Input"，可能有"Select"）
  dictionary: Map<string, string>; // 字典值（key是值，value是文字）
  required: boolean;   // 是否必填
}
```

## 修改计划
1. 修改 data.ts，将 QueryFormSchema 改为动态生成
2. 修改 index.vue，添加监听 exchangeServiceId 变化的逻辑
3. 调用 apiGaggsjViewInvokeParam 接口获取动态字段配置
4. 根据配置动态生成表单 schema
5. 支持 Input 和 Select 组件类型
6. 处理必填校验和字典选项

## 执行结果
已成功完成动态生成查询条件的功能：

### 1. data.ts 修改
- ✅ 将 `QueryFormSchema` 改名为 `BaseQueryFormSchema`
- ✅ 添加了 `ViewInvokeParam` 接口类型定义
- ✅ 添加了 `generateFormSchemaFromParams` 函数，支持根据配置生成表单字段
- ✅ 支持 Input 和 Select 组件类型
- ✅ 处理字典选项和必填校验

### 2. index.vue 修改
- ✅ 添加了必要的导入：`apiGaggsjViewInvokeParam`、`BaseQueryFormSchema`、`generateFormSchemaFromParams`、`ViewInvokeParam`
- ✅ 创建了动态表单 schema：`dynamicFormSchema`
- ✅ 添加了 `loadDynamicFields` 函数，根据 exchangeServiceId 加载动态字段
- ✅ 添加了 watch 监听器，监听 exchangeServiceId 变化
- ✅ 当用户选择业务及材料类型时，自动调用接口获取查询条件并更新表单

### 3. 功能特性
- ✅ 根据业务及材料类型动态生成查询条件
- ✅ 支持 Input 组件（默认）
- ✅ 支持 Select 组件，根据 dictionary 生成选项（key是值，value是文字）
- ✅ 根据 required 字段校验必填
- ✅ 实时更新表单 schema
- ✅ 错误处理和加载状态
