# 动态查询条件监听功能

## 任务描述
根据业务及材料类型字段的选择，查询 apiGaggsjViewInvokeParam 接口，获取查询条件，根据 component 类型生成查询组件，如果是 Select，根据 dictionary 生成下拉框，key 是值，value 是文字，还要根据 required 字段校验必填。

## 上下文信息
- 主文件：`apps/web-naive/src/views/business/sjspsl/dzgxclcx/index.vue`
- 数据文件：`apps/web-naive/src/views/business/sjspsl/dzgxclcx/data.ts`
- 接口：`apiGaggsjViewInvokeParam`
- 当前状态：基础功能已实现，缺少字段变化监听

## 执行计划
1. 添加 exchangeServiceId 字段变化监听
2. 优化动态字段加载逻辑
3. 验证字典数据处理
4. 验证必填校验
5. 测试功能完整性

## 实现方案
使用 handleValuesChange 监听 exchangeServiceId 字段变化，自动调用 loadDynamicFields 函数加载对应的查询条件。

## 执行结果
✅ 已完成所有功能实现：

### 1. 动态查询条件功能
- 添加了 exchangeServiceId 字段变化监听（通过 handleValuesChange）
- 优化了动态字段加载逻辑（清空其他字段值）
- 验证了字典数据处理（key作为value，value作为label）
- 验证了必填校验（根据required字段设置rules）
- 添加了 schema 动态更新监听

### 2. 页面样式优化
- 查询表单区域：白色背景、圆角边框、阴影效果
- 结果展示区域：统一的卡片样式
- 加载状态：居中显示，卡片样式
- 使用系统设计令牌：`bg-card`、`border-border`、`text-muted-foreground` 等

### 3. 主要功能特性
- 当用户选择不同的业务及材料类型时，自动调用 apiGaggsjViewInvokeParam 接口
- 根据返回的 ViewInvokeParam 数组动态生成查询组件
- Select 组件根据 dictionary 生成下拉选项
- 根据 required 字段进行必填校验
- 切换业务类型时自动清空其他字段值
- 页面样式符合常口系统设计规范
